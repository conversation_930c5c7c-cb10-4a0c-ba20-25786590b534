"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import {
  Bot,
  Castle,
  Heart,
  History,
  Home,
  MessageCircle,
  Rocket,
  Settings,
  Star,
  Sword,
  Users,
} from "lucide-react"

import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  navMain: [
    {
      title: "Eksplor",
      url: "/dashboard",
      icon: Home,
      description: "Jelajahi karakter AI",
    },
    {
      title: "<PERSON><PERSON> <PERSON><PERSON>",
      url: "/chat",
      icon: MessageCircle,
      description: "Lanjutkan percakapan",
    },
    {
      title: "Favorit",
      url: "#",
      icon: Heart,
      description: "Karakter tersimpan",
    },
    {
      title: "Pengaturan",
      url: "#",
      icon: Settings,
      description: "Akun & preferensi",
    },
  ],
  quickActions: [
    {
      name: "<PERSON><PERSON><PERSON>",
      url: "#",
      icon: Castle,
      color: "from-purple-500 to-pink-500",
    },
    {
      name: "Romantis",
      url: "#",
      icon: Heart,
      color: "from-pink-500 to-rose-500",
    },
    {
      name: "Petualangan",
      url: "#",
      icon: Sword,
      color: "from-orange-500 to-red-500",
    },
    {
      name: "Sci-Fi",
      url: "#",
      icon: Rocket,
      color: "from-blue-500 to-cyan-500",
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()

  return (
    <Sidebar variant="inset" {...props} className="border-r-0">
      <SidebarHeader className="border-b-0 p-6">
        <a href="/dashboard" className="group flex items-center">
          <div className="bg-gradient-to-br from-[#2DD4BF] to-[#14B8A6] text-white flex aspect-square size-12 items-center justify-center rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
            <Bot className="size-6" />
          </div>
          <div className="grid flex-1 text-left leading-tight ml-3">
            <span className="font-bold text-lg bg-gradient-to-r from-[#2DD4BF] to-[#14B8A6] bg-clip-text text-transparent">
              Bestieku
            </span>
            <span className="text-xs text-muted-foreground font-medium">
              AI Character Chat
            </span>
          </div>
        </a>
      </SidebarHeader>

      <SidebarContent className="px-4">
        {/* Main Navigation */}
        <div className="space-y-2 mb-8">
          <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3">
            Navigasi
          </h3>
          {data.navMain.map((item) => {
            const isActive = pathname === item.url
            return (
              <div key={item.title}>
                <a
                  href={item.url}
                  className={`flex items-center gap-3 px-3 h-12 rounded-xl transition-all duration-200 hover:bg-gradient-to-r hover:from-[#2DD4BF]/10 hover:to-[#14B8A6]/10 hover:scale-[1.02] group ${
                    isActive ? 'bg-gradient-to-r from-[#2DD4BF]/20 to-[#14B8A6]/20 border border-[#2DD4BF]/30' : ''
                  }`}
                >
                  <div className={`p-2 rounded-lg ${
                    isActive
                      ? 'bg-[#2DD4BF] text-white shadow-md'
                      : 'bg-muted/50 text-muted-foreground group-hover:bg-[#2DD4BF]/20 group-hover:text-[#2DD4BF]'
                  } transition-all duration-200`}>
                    <item.icon className="size-4" />
                  </div>
                  <div className="flex-1">
                    <div className={`font-medium ${isActive ? 'text-[#2DD4BF]' : ''}`}>
                      {item.title}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {item.description}
                    </div>
                  </div>
                </a>
              </div>
            )
          })}
        </div>

        {/* Quick Categories */}
        <div className="space-y-3">
          <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3">
            Kategori Cepat
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {data.quickActions.map((action) => (
              <a
                key={action.name}
                href={action.url}
                className={`p-3 rounded-xl bg-gradient-to-br ${action.color} text-white hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg group flex flex-col items-center`}
              >
                <action.icon className="w-5 h-5 mb-1" />
                <div className="text-xs font-medium">{action.name}</div>
              </a>
            ))}
          </div>
        </div>
      </SidebarContent>

      <SidebarFooter className="p-4 border-t-0">
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
