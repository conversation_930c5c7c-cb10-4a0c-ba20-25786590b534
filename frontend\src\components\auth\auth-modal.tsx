'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/auth-context';
import { SignInRequest, SignUpRequest, VerifyOTPRequest } from '@/types/auth';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type AuthStep = 'signin' | 'signup' | 'verify-otp';

export function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const [step, setStep] = useState<AuthStep>('signin');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingAuth, setPendingAuth] = useState<{ email: string; provider: string } | null>(null);
  
  const { signIn, signUp, verifyOTP } = useAuth();

  const [signInData, setSignInData] = useState<SignInRequest>({
    provider: 'email',
    email: '',
  });

  const [signUpData, setSignUpData] = useState<SignUpRequest>({
    provider: 'email',
    name: '',
    email: '',
    dateOfBirth: '',
    gender: '',
    about: '',
  });

  const [otpCode, setOtpCode] = useState('');

  const resetForm = () => {
    setSignInData({ provider: 'email', email: '' });
    setSignUpData({ provider: 'email', name: '', email: '', dateOfBirth: '', gender: '', about: '' });
    setOtpCode('');
    setError(null);
    setPendingAuth(null);
    setStep('signin');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await signIn(signInData);
      setPendingAuth({ email: signInData.email!, provider: signInData.provider });
      setStep('verify-otp');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sign in failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await signUp(signUpData);
      setPendingAuth({ email: signUpData.email!, provider: signUpData.provider });
      setStep('verify-otp');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sign up failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!pendingAuth) return;

    setIsLoading(true);
    setError(null);

    try {
      const verifyData: VerifyOTPRequest = {
        provider: pendingAuth.provider as 'email',
        email: pendingAuth.email,
        code: otpCode,
      };
      
      await verifyOTP(verifyData);
      handleClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'OTP verification failed');
    } finally {
      setIsLoading(false);
    }
  };

  const renderSignInForm = () => (
    <form onSubmit={handleSignIn} className="space-y-4">
      <div>
        <label htmlFor="signin-email" className="block text-sm font-medium mb-2">
          Email
        </label>
        <Input
          id="signin-email"
          type="email"
          value={signInData.email}
          onChange={(e) => setSignInData({ ...signInData, email: e.target.value })}
          placeholder="Enter your email"
          required
        />
      </div>
      
      {error && (
        <div className="text-red-500 text-sm">{error}</div>
      )}
      
      <Button type="submit" className="w-full bg-[#2DD4BF] hover:bg-[#14B8A6] text-white" disabled={isLoading}>
        {isLoading ? 'Sending OTP...' : 'Send OTP'}
      </Button>
      
      <div className="text-center">
        <button
          type="button"
          onClick={() => setStep('signup')}
          className="text-sm text-[#2DD4BF] hover:text-[#14B8A6] hover:underline"
        >
          Don't have an account? Sign up
        </button>
      </div>
    </form>
  );

  const renderSignUpForm = () => (
    <form onSubmit={handleSignUp} className="space-y-4">
      <div>
        <label htmlFor="signup-name" className="block text-sm font-medium mb-2">
          Name *
        </label>
        <Input
          id="signup-name"
          type="text"
          value={signUpData.name}
          onChange={(e) => setSignUpData({ ...signUpData, name: e.target.value })}
          placeholder="Enter your full name (min 3 characters)"
          required
          minLength={3}
        />
      </div>

      <div>
        <label htmlFor="signup-email" className="block text-sm font-medium mb-2">
          Email *
        </label>
        <Input
          id="signup-email"
          type="email"
          value={signUpData.email}
          onChange={(e) => setSignUpData({ ...signUpData, email: e.target.value })}
          placeholder="Enter your email"
          required
        />
      </div>

      <div>
        <label htmlFor="signup-dob" className="block text-sm font-medium mb-2">
          Date of Birth
        </label>
        <Input
          id="signup-dob"
          type="date"
          value={signUpData.dateOfBirth}
          onChange={(e) => setSignUpData({ ...signUpData, dateOfBirth: e.target.value })}
        />
      </div>

      <div>
        <label htmlFor="signup-gender" className="block text-sm font-medium mb-2">
          Gender
        </label>
        <select
          id="signup-gender"
          value={signUpData.gender}
          onChange={(e) => setSignUpData({ ...signUpData, gender: e.target.value })}
          className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
        >
          <option value="">Select gender</option>
          <option value="male">Male</option>
          <option value="female">Female</option>
          <option value="other">Other</option>
        </select>
      </div>

      <div>
        <label htmlFor="signup-about" className="block text-sm font-medium mb-2">
          About
        </label>
        <textarea
          id="signup-about"
          value={signUpData.about}
          onChange={(e) => setSignUpData({ ...signUpData, about: e.target.value })}
          placeholder="Tell us about yourself (optional)"
          className="flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
          rows={3}
        />
      </div>

      {error && (
        <div className="text-red-500 text-sm">{error}</div>
      )}

      <Button type="submit" className="w-full bg-[#2DD4BF] hover:bg-[#14B8A6] text-white" disabled={isLoading}>
        {isLoading ? 'Sending OTP...' : 'Sign Up'}
      </Button>

      <div className="text-center">
        <button
          type="button"
          onClick={() => setStep('signin')}
          className="text-sm text-[#2DD4BF] hover:text-[#14B8A6] hover:underline"
        >
          Already have an account? Sign in
        </button>
      </div>
    </form>
  );

  const renderOTPForm = () => (
    <form onSubmit={handleVerifyOTP} className="space-y-4">
      <div className="text-center mb-4">
        <p className="text-sm text-gray-600">
          We've sent a verification code to {pendingAuth?.email}
        </p>
      </div>
      
      <div>
        <label htmlFor="otp-code" className="block text-sm font-medium mb-2">
          Verification Code
        </label>
        <Input
          id="otp-code"
          type="text"
          value={otpCode}
          onChange={(e) => setOtpCode(e.target.value)}
          placeholder="Enter 6-digit code"
          maxLength={6}
          required
        />
      </div>
      
      {error && (
        <div className="text-red-500 text-sm">{error}</div>
      )}
      
      <Button type="submit" className="w-full bg-[#2DD4BF] hover:bg-[#14B8A6] text-white" disabled={isLoading}>
        {isLoading ? 'Verifying...' : 'Verify Code'}
      </Button>
      
      <div className="text-center">
        <button
          type="button"
          onClick={() => setStep('signin')}
          className="text-sm text-[#2DD4BF] hover:text-[#14B8A6] hover:underline"
        >
          Back to sign in
        </button>
      </div>
    </form>
  );

  const getTitle = () => {
    switch (step) {
      case 'signin':
        return 'Sign In';
      case 'signup':
        return 'Sign Up';
      case 'verify-otp':
        return 'Verify Email';
      default:
        return 'Authentication';
    }
  };

  const renderContent = () => {
    switch (step) {
      case 'signin':
        return renderSignInForm();
      case 'signup':
        return renderSignUpForm();
      case 'verify-otp':
        return renderOTPForm();
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
        </DialogHeader>
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
}
