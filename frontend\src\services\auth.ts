import {
  SignInRequest,
  SignUpRequest,
  VerifyOTPRequest,
  AuthResponse,
  User,
  UpdateProfileRequest
} from '@/types/auth';
import { env } from '@/lib/env';

class AuthService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async signIn(data: SignInRequest): Promise<void> {
    const response = await fetch(`${env.API_BASE_URL}/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Sign in failed');
    }

    // OTP sent successfully, no response data needed
  }

  async signUp(data: SignUpRequest): Promise<void> {
    const response = await fetch(`${env.API_BASE_URL}/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Sign up failed');
    }

    // OTP sent successfully, no response data needed
  }

  async verifyOTP(data: VerifyOTPRequest): Promise<AuthResponse> {
    const response = await fetch(`${env.API_BASE_URL}/auth/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'OTP verification failed');
    }

    const authResponse: AuthResponse = await response.json();
    
    // Store token in localStorage
    localStorage.setItem('accessToken', authResponse.accessToken);
    localStorage.setItem('tokenExpiredAt', authResponse.expiredAt.toString());
    
    return authResponse;
  }

  async getProfile(): Promise<User> {
    const response = await fetch(`${env.API_BASE_URL}/auth/profile`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get profile');
    }

    return await response.json();
  }

  async updateProfile(data: UpdateProfileRequest): Promise<User> {
    const response = await fetch(`${env.API_BASE_URL}/auth/profile`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update profile');
    }

    return await response.json();
  }

  async uploadProfileImage(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    const token = localStorage.getItem('accessToken');
    const response = await fetch(`${env.API_BASE_URL}/auth/profile/image`, {
      method: 'PUT',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to upload image');
    }

    const result = await response.json();
    return result.url;
  }

  logout(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('tokenExpiredAt');
  }

  isTokenValid(): boolean {
    const token = localStorage.getItem('accessToken');
    const expiredAt = localStorage.getItem('tokenExpiredAt');
    
    if (!token || !expiredAt) {
      return false;
    }

    const now = Math.floor(Date.now() / 1000);
    return parseInt(expiredAt) > now;
  }

  getToken(): string | null {
    return this.isTokenValid() ? localStorage.getItem('accessToken') : null;
  }
}

export const authService = new AuthService();
