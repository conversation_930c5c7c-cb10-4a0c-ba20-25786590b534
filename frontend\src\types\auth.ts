export interface User {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  image?: string;
  dateOfBirth?: string;
  gender?: string;
  about?: string;
  status: string;
}

export interface AuthResponse {
  accessToken: string;
  expiredAt: number;
  issuedAt: number;
}

export interface SignInRequest {
  provider: 'email' | 'whatsapp' | 'sms';
  email?: string;
  phoneNumber?: string;
}

export interface SignUpRequest {
  provider: 'email' | 'whatsapp' | 'sms';
  name: string;
  email?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  gender?: string;
  about?: string;
}

export interface VerifyOTPRequest {
  provider: 'email' | 'whatsapp' | 'sms';
  email?: string;
  phoneNumber?: string;
  code: string;
}

export interface UpdateProfileRequest {
  name: string;
  dateOfBirth?: string;
  gender?: string;
  about?: string;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  signIn: (data: SignInRequest) => Promise<void>;
  signUp: (data: SignUpRequest) => Promise<void>;
  verifyOTP: (data: VerifyOTPRequest) => Promise<void>;
  logout: () => void;
  updateProfile: (data: UpdateProfileRequest) => Promise<void>;
  uploadProfileImage: (file: File) => Promise<string>;
}
