'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Chat, Message, SSEEvent } from '@/types/chat';
import { chatService } from '@/services/chat';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send, MoreVertical } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ChatInterfaceProps {
  chat: Chat;
  onBack?: () => void;
}

export function ChatInterface({ chat, onBack }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [streamingMessage, setStreamingMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const streamConnectionRef = useRef<{ close: () => void } | null>(null);

  useEffect(() => {
    loadMessages();
    return () => {
      // Cleanup stream connection on unmount
      if (streamConnectionRef.current) {
        streamConnectionRef.current.close();
      }
    };
  }, [chat.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, streamingMessage]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await chatService.getChatMessages(chat.id, { limit: 50 });
      setMessages(response.data.reverse()); // Reverse to show oldest first
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    const messageText = newMessage.trim();
    const tempId = `temp-${Date.now()}`;
    setNewMessage('');
    setSending(true);

    try {
      // Add user message to UI immediately
      const userMessage: Message = {
        id: tempId,
        role: 'user',
        content: messageText,
        contentType: 'text',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setMessages(prev => [...prev, userMessage]);

      console.log('Sending message to chat:', chat.id, { message: messageText, streaming: true });

      // Send message with streaming enabled
      const response = await chatService.sendMessage(chat.id, {
        message: messageText,
        streaming: true,
      });

      console.log('Message sent successfully:', response);

      // Update the temporary message with real ID if available
      if (response.id) {
        setMessages(prev => prev.map(msg =>
          msg.id === tempId ? { ...msg, id: response.id } : msg
        ));
      }

      // Start streaming response
      await startStreaming();

    } catch (error) {
      console.error('Failed to send message:', error);
      // Remove the temporary user message on error
      setMessages(prev => prev.filter(msg => msg.id !== tempId));
      alert('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };

  const startStreaming = async () => {
    console.log('Starting stream for chat:', chat.id);
    setIsStreaming(true);
    setStreamingMessage('');

    // Close existing connection
    if (streamConnectionRef.current) {
      streamConnectionRef.current.close();
    }

    let currentStreamingMessage = '';

    const onMessage = (data: SSEEvent) => {
      console.log('Received SSE event:', data);

      switch (data.event) {
        case 'start':
          console.log('Stream started');
          currentStreamingMessage = '';
          setStreamingMessage('');
          break;
        case 'token':
          currentStreamingMessage += data.data;
          setStreamingMessage(currentStreamingMessage);
          break;
        case 'metadata':
          console.log('Stream metadata:', data.data);
          break;
        case 'end':
          console.log('Stream ended, final message:', currentStreamingMessage);
          // Finalize the streaming message
          const finalMessage: Message = {
            id: data.data?.chatMessageId || `msg-${Date.now()}`,
            role: 'assistant',
            content: currentStreamingMessage,
            contentType: 'text',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          setMessages(prev => [...prev, finalMessage]);
          setStreamingMessage('');
          setIsStreaming(false);
          if (streamConnectionRef.current) {
            streamConnectionRef.current.close();
          }
          break;
        default:
          console.log('Unknown SSE event:', data.event);
      }
    };

    const onError = async (error: any) => {
      console.error('Stream Error:', error);
      setIsStreaming(false);
      setStreamingMessage('');
      if (streamConnectionRef.current) {
        streamConnectionRef.current.close();
      }

      // Fallback: try to reload messages to get the AI response
      console.log('Attempting to reload messages as fallback...');
      try {
        await loadMessages();
      } catch (reloadError) {
        console.error('Failed to reload messages:', reloadError);
        alert('Failed to receive AI response. Please try again.');
      }
    };

    try {
      // Create new stream connection
      const connection = await chatService.createStreamConnection(chat.id, onMessage, onError);
      streamConnectionRef.current = connection;

      // Set timeout for streaming (30 seconds)
      setTimeout(() => {
        if (isStreaming) {
          console.log('Stream timeout, falling back to message reload');
          onError(new Error('Stream timeout'));
        }
      }, 30000);

    } catch (error) {
      console.error('Failed to create stream connection:', error);
      setIsStreaming(false);
      alert('Failed to establish connection for AI response. Please try again.');
    }
  };

  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return '';
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col h-full overflow-hidden">
        <div className="flex-shrink-0 border-b p-4 animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-muted rounded-full" />
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded w-32" />
              <div className="h-3 bg-muted rounded w-20" />
            </div>
          </div>
        </div>
        <div className="flex-1 p-4 space-y-4 overflow-y-auto">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex space-x-3 animate-pulse">
              <div className="w-8 h-8 bg-muted rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-4 bg-muted rounded w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Chat Header - Fixed */}
      <div className="flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar className="w-12 h-12 ring-2 ring-[#2DD4BF]/20">
                <AvatarImage src={chat.character?.image} alt={chat.character?.name} />
                <AvatarFallback className="bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold">
                  {chat.character?.name?.charAt(0)?.toUpperCase() || 'C'}
                </AvatarFallback>
              </Avatar>
              {/* Online indicator */}
              <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full"></div>
            </div>
            <div>
              <h2 className="font-semibold text-lg">{chat.character?.name || 'Unknown Character'}</h2>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{chat.messageCount} messages</span>
                <span>•</span>
                <span className="text-green-500">Online</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="hover:bg-muted/50">
              <MoreVertical className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages - Scrollable */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20">
        {messages.map((message, index) => (
          <div
            key={message.id}
            className={`flex items-end gap-2 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {/* Character Avatar for assistant messages */}
            {message.role === 'assistant' && (
              <Avatar className="w-8 h-8 mb-1">
                <AvatarImage src={chat.character?.image} alt={chat.character?.name} />
                <AvatarFallback className="text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]">
                  {chat.character?.name?.charAt(0)?.toUpperCase() || 'C'}
                </AvatarFallback>
              </Avatar>
            )}

            <div
              className={`max-w-[70%] rounded-2xl p-4 shadow-sm ${
                message.role === 'user'
                  ? 'bg-[#2DD4BF] text-white rounded-br-md'
                  : 'bg-white dark:bg-muted border rounded-bl-md'
              }`}
            >
              <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
              <div className={`flex items-center justify-between mt-2 ${
                message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'
              }`}>
                <p className="text-xs">
                  {formatTime(message.createdAt)}
                </p>
                {message.role === 'user' && (
                  <div className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-white/70 rounded-full"></div>
                    <div className="w-1 h-1 bg-white/70 rounded-full"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Streaming Message */}
        {isStreaming && (
          <div className="flex items-end gap-2 justify-start">
            <Avatar className="w-8 h-8 mb-1">
              <AvatarImage src={chat.character?.image} alt={chat.character?.name} />
              <AvatarFallback className="text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]">
                {chat.character?.name?.charAt(0)?.toUpperCase() || 'C'}
              </AvatarFallback>
            </Avatar>

            <div className="max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm">
              {streamingMessage ? (
                <p className="text-sm whitespace-pre-wrap leading-relaxed">{streamingMessage}</p>
              ) : (
                <div className="flex items-center gap-1">
                  <span className="text-sm text-muted-foreground">Thinking</span>
                  <div className="flex space-x-1 ml-2">
                    <div className="w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              )}

              <div className="flex items-center mt-2 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <div className="w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse"></div>
                  <span className="text-xs">Typing...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input - Fixed */}
      <div className="flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-end space-x-3">
          <div className="flex-1 relative">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={`Message ${chat.character?.name || 'character'}...`}
              disabled={sending || isStreaming}
              className="pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors"
            />
            {/* Character count or status */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {(sending || isStreaming) && (
                <div className="w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin"></div>
              )}
            </div>
          </div>

          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending || isStreaming}
            className="bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>

        {/* Quick actions or suggestions */}
        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          {isStreaming && (
            <span className="text-[#2DD4BF] animate-pulse">AI is responding...</span>
          )}
        </div>
      </div>
    </div>
  );
}
