import {
  Chat,
  ChatsResponse,
  Message,
  MessagesResponse,
  SendMessageRequest,
  SendMessageResponse,
  GetChatsParams,
  GetMessagesParams,
  SSEEvent
} from '@/types/chat';
import { env } from '@/lib/env';

class ChatService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async getChats(params: GetChatsParams = {}): Promise<ChatsResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);

    const url = `${env.API_BASE_URL}/chats${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch chats');
    }

    return await response.json();
  }

  async getChatById(chatId: string): Promise<Chat> {
    const response = await fetch(`${env.API_BASE_URL}/chats/${chatId}`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch chat');
    }

    return await response.json();
  }

  async getChatMessages(chatId: string, params: GetMessagesParams = {}): Promise<MessagesResponse> {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.search) searchParams.append('search', params.search);

    const url = `${env.API_BASE_URL}/chats/${chatId}/messages${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch messages');
    }

    return await response.json();
  }

  async sendMessage(chatId: string, data: SendMessageRequest): Promise<SendMessageResponse> {
    console.log('Sending message to API:', `${env.API_BASE_URL}/chats/${chatId}`, data);

    const response = await fetch(`${env.API_BASE_URL}/chats/${chatId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    console.log('Send message response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Send message error response:', errorText);

      try {
        const error = JSON.parse(errorText);
        throw new Error(error.message || 'Failed to send message');
      } catch {
        throw new Error(`Failed to send message: ${response.status} ${response.statusText}`);
      }
    }

    const result = await response.json();
    console.log('Send message success response:', result);
    return result;
  }

  // Create SSE connection for streaming chat using fetch
  async createStreamConnection(chatId: string, onMessage: (data: any) => void, onError: (error: any) => void) {
    console.log('Creating stream connection for chat:', chatId);

    try {
      const response = await fetch(`${env.API_BASE_URL}/chats/${chatId}/stream`, {
        method: 'GET',
        headers: {
          ...this.getAuthHeaders(),
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
      });

      console.log('Stream connection response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Stream connection error:', errorText);
        throw new Error(`Failed to establish stream connection: ${response.status} ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('No response body for stream');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      const readStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              console.log('Stream reading completed');
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // Process complete lines
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep incomplete line in buffer

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6).trim();
                console.log('Received SSE data:', data);

                if (data && data !== '[DONE]') {
                  try {
                    const parsed = JSON.parse(data);
                    onMessage(parsed);
                  } catch (e) {
                    console.error('Error parsing SSE data:', e, 'Raw data:', data);
                  }
                } else if (data === '[DONE]') {
                  console.log('Stream completed with [DONE]');
                  onMessage({ event: 'end', data: '[DONE]' });
                  break;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream reading error:', error);
          onError(error);
        }
      };

      readStream();

      return {
        close: () => {
          console.log('Closing stream connection');
          reader.cancel();
        }
      };
    } catch (error) {
      console.error('Failed to create stream connection:', error);
      onError(error);
      return {
        close: () => {}
      };
    }
  }

  // Alternative method using fetch for SSE with proper headers
  async createStreamConnectionWithFetch(chatId: string): Promise<ReadableStream> {
    const response = await fetch(`${env.API_BASE_URL}/chats/${chatId}/stream`, {
      method: 'GET',
      headers: {
        ...this.getAuthHeaders(),
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to establish stream connection');
    }

    if (!response.body) {
      throw new Error('No response body for stream');
    }

    return response.body;
  }
}

export const chatService = new ChatService();
