'use client';

import React, { useState, useEffect } from 'react';
import { Character } from '@/types/character';
import { characterService } from '@/services/character';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar, 
  Tag, 
  MessageCircle, 
  Star, 
  Info,
  X,
  ChevronRight,
  ChevronLeft
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface CharacterProfileSidebarProps {
  characterId: string;
  messageCount: number;
  isOpen: boolean;
  onToggle: () => void;
}

export function CharacterProfileSidebar({ 
  characterId, 
  messageCount, 
  isOpen, 
  onToggle 
}: CharacterProfileSidebarProps) {
  const [character, setCharacter] = useState<Character | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCharacter();
  }, [characterId]);

  const loadCharacter = async () => {
    try {
      setLoading(true);
      const characterData = await characterService.getCharacterById(characterId);
      setCharacter(characterData);
    } catch (error) {
      console.error('Failed to load character:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className={`bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden ${
        isOpen ? 'w-80' : 'w-12'
      }`}>
        <div className="flex-shrink-0 p-4 border-b">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="w-full"
          >
            {isOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>

        {isOpen && (
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-4 animate-pulse">
              <div className="w-full h-32 bg-muted rounded-lg" />
              <div className="h-6 bg-muted rounded w-3/4" />
              <div className="h-4 bg-muted rounded w-full" />
              <div className="h-4 bg-muted rounded w-2/3" />
            </div>
          </div>
        )}
      </div>
    );
  }

  if (!character) {
    return (
      <div className={`bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden ${
        isOpen ? 'w-80' : 'w-12'
      }`}>
        <div className="flex-shrink-0 p-4 border-b">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="w-full"
          >
            {isOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>

        {isOpen && (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center text-muted-foreground">
              <Info className="w-8 h-8 mx-auto mb-2" />
              <p className="text-sm">Character not found</p>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-background border-l transition-all duration-300 flex flex-col h-full overflow-hidden ${
      isOpen ? 'w-80' : 'w-12'
    }`}>
      {/* Toggle Button - Fixed */}
      <div className="flex-shrink-0 p-4 border-b">
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="w-full"
        >
          {isOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </Button>
      </div>

      {/* Profile Content - Scrollable */}
      {isOpen && (
        <div className="flex-1 overflow-y-auto overflow-x-hidden p-4">
          <div className="space-y-6">
            {/* Character Avatar & Name */}
            <div className="text-center">
              <Avatar className="w-24 h-24 mx-auto mb-4">
                <AvatarImage src={character.image} alt={character.name} />
                <AvatarFallback className="text-2xl">
                  {character.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <h2 className="text-xl font-bold mb-2">{character.name}</h2>
              
              <div className="flex items-center justify-center gap-2 mb-3">
                {character.storyMode && (
                  <Badge className="bg-[#2DD4BF] text-white">
                    Story Mode
                  </Badge>
                )}
                <Badge variant="outline" className="capitalize">
                  {character.status}
                </Badge>
              </div>
            </div>

            <Separator />

            {/* Description */}
            <div>
              <h3 className="font-semibold mb-2 flex items-center gap-2">
                <Info className="w-4 h-4" />
                About
              </h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {character.description}
              </p>
            </div>

            <Separator />

            {/* Tags */}
            {character.tags && character.tags.length > 0 && (
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  Tags
                </h3>
                <div className="flex flex-wrap gap-2">
                  {character.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            {/* Chat Stats */}
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <MessageCircle className="w-4 h-4" />
                Chat Stats
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Messages</span>
                  <span className="text-sm font-medium">{messageCount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Character Type</span>
                  <span className="text-sm font-medium">
                    {character.storyMode ? 'Story' : 'Chat'}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Character Info */}
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Character Info
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Created</span>
                  <span className="text-sm font-medium">
                    {formatDate(character.createdAt)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Updated</span>
                  <span className="text-sm font-medium">
                    {formatDate(character.updatedAt)}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Action Buttons */}
            <div className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => {
                  // TODO: Add to favorites functionality
                  alert('Add to favorites feature coming soon!');
                }}
              >
                <Star className="w-4 h-4 mr-2" />
                Add to Favorites
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => {
                  // TODO: Share character functionality
                  alert('Share character feature coming soon!');
                }}
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Share Character
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
