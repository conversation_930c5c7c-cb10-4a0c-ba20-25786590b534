export interface Character {
  id: string;
  name: string;
  description: string;
  storyMode: boolean;
  status: string;
  image: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CharactersResponse {
  data: Character[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export interface GetCharactersParams {
  page?: number;
  limit?: number;
  search?: string;
  tags?: string;
  storyMode?: string;
}

export interface ChatSession {
  id: string;
  userId: string;
  characterId: string;
  messageCount: number;
  platform: string;
  createdAt: string;
  updatedAt: string;
}
