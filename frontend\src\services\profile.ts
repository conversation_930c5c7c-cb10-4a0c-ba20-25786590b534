import { apiClient } from './api';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  image?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  about?: string;
  status: string;
}

export interface UpdateProfileRequest {
  name: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  about?: string;
}

export interface UploadImageResponse {
  url: string;
  key: string;
}

class ProfileService {
  async getProfile(): Promise<UserProfile> {
    const response = await apiClient.get('/auth/profile');
    return response.data;
  }

  async updateProfile(data: UpdateProfileRequest): Promise<UserProfile> {
    const response = await apiClient.put('/auth/profile', data);
    return response.data;
  }

  async uploadProfileImage(file: File): Promise<UploadImageResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.put('/auth/profile/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
}

export const profileService = new ProfileService();
